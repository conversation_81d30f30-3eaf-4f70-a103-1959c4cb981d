import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { ButtonModule } from 'primeng/button';
import { NoDataComponent } from "../no-data/no-data";
import { SharedModule } from 'src/app/shared.module';
import { TooltipModule } from 'primeng/tooltip';
import { ImageModule } from 'primeng/image';
import { SkeletonComponent } from "../skeleton/skeleton.component";

@Component({
  selector: 'app-table',
  standalone: true,
  templateUrl: './table.html',
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    InputNumberModule,
    ButtonModule,
    NoDataComponent,
    SharedModule,
    TooltipModule,
    ImageModule,
    SkeletonComponent
],
})
export class TableComponent implements OnChanges {
  @Input() loading = false;
  @Input() scrollable = true;
  @Input() pagination = true;
  @Input() scrollHeight: string = 'calc(100vh - 160px)';
  @Input() tableData: any[] = [];
  @Input() tableColumns: any[] = [];
  @Input() selectedRows: any[] = [];
  @Input() dataKey: string = '';
  @Output() onChange = new EventEmitter<any>();
  @Output() onActionClick = new EventEmitter<any>();
  @Output() onRowClick = new EventEmitter<any>();


  ngOnChanges(changes: SimpleChanges) {
    if (changes['tableData'] && this.tableData && this.tableData.length > 0) {
      this.tableData.forEach(product => {
        if (!product.quantity) {
          product.quantity = 1;
        }
      });
    }
  }

  onInputChange(event: any, rowData: any, column: any) {
    this.onChange.emit({ event, rowData, column });
  }
  
  buttonClick(button: any, rowData: any) {
    this.onActionClick.emit({ button, rowData });
  }

  rowClick(event: any, rowData: any, column: any) {
    this.onRowClick.emit({ event, rowData, column });
  }

  getModel(rowData: any, col: any){
    return col.body ? col.body(rowData) : rowData[col.field];
  }
}
