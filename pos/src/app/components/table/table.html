<div class="w-full" *ngIf="!loading">
  <p-table #table showGridlines stripedRows [paginator]="pagination" [rows]="10"
    [rowsPerPageOptions]="[10, 20, 50, 100]" [value]="tableData" [dataKey]="dataKey" [scrollable]="scrollable"
    [scrollHeight]="scrollHeight" [virtualScroll]="false">
    <ng-template #header>
      <tr>
        <th *ngFor="let col of tableColumns" [style]="col.style">
          <div class="flex items-center text-sm">
            {{col.header}}
          </div>
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-rowIndex="rowIndex" let-columns="columns">
      <tr class="cursor-pointer transition-colors">
        <ng-container *ngFor="let col of tableColumns">
          <td *ngIf="col.type === 'image'" [style.width]="col.width" [style]="col.style">
            <div class="flex h-full">
              <ng-container>
                <p-image [appendTo]="'body'" [preview]="true" [src]="rowData[col.field]" alt="Image" width="40"
                  height="40" />
              </ng-container>
            </div>
          </td>

          <td *ngIf="col.type === 'quantity'" [style.width]="col.width" [style]="col.style">
            <div class="flex h-full">
              <ng-container>
                <p-inputnumber [(ngModel)]="rowData[col.field]" [showButtons]="true" buttonLayout="horizontal"
                  spinnerMode="horizontal" [min]="1" [max]="999"
                  [inputStyle]="{ width: '3rem', height: '1.8rem', textAlign: 'center', fontSize: '0.75rem' }"
                  (onInput)="onInputChange($event, rowData, col)">
                  <ng-template #incrementbuttonicon>
                    <span class="pi pi-plus text-xs"></span>
                  </ng-template>
                  <ng-template #decrementbuttonicon>
                    <span class="pi pi-minus text-xs"></span>
                  </ng-template>
                </p-inputnumber>
              </ng-container>
            </div>
          </td>

          <td *ngIf="col.type === 'action'" [style.width]="col.width" [style]="col.style" class="leading-none">
            <div class="flex h-full justify-center">
              <ng-container>
                <ng-container *ngFor="let button of col.buttons">
                  <button size="small" [appDisableDoubleClick]="button.disableDoubleClick || false" pButton
                    [icon]="button.icon" [class]="button.class" [severity]="button.severity"
                    [outlined]="button.outlined" [rounded]="button.rounded" [label]="button.label" *ngIf="!button.hide"
                    (click)="buttonClick(button, rowData)"></button>
                </ng-container>
              </ng-container>
            </div>
          </td>

          <td *ngIf="!col.type" [style.width]="col.width" [style]="col.style" (click)="rowClick($event, rowData, col)">
            <div class="flex h-full">
              <span class="line-clamp-2 text-sm" [pTooltip]="getModel(rowData, col)">{{getModel(rowData, col)}}</span>
            </div>
          </td>
        </ng-container>

      </tr>
    </ng-template>

    <ng-template #emptymessage>
      <tr>
        <td colspan="100%">
          <div class="p-10">
            <app-no-data></app-no-data>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<div class="w-full" *ngIf="loading">
  <app-skeleton [table]="true"></app-skeleton>
</div>